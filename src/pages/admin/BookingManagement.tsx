import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import { AdminBookingInfo, Booking, BookingStatus } from '@/types/help';
import { GroupedBooking, groupConsecutiveBookings } from '@/utils/bookingGrouping';
import { formatBookingGroupTime, calculateBookingGroupTotalPrice } from '@/utils/bookingGroupTimeFormatting';
import BookingsList from '@/components/admin/BookingsList';
import AdminBookingTab from '@/components/admin/AdminBookingTab';
import SlotBlockingTab from '@/components/admin/SlotBlockingTab';
import { protectedBookingUpdate } from '@/utils/protectedSupabase';
import { logSecurityEvent } from '@/utils/adminSecurity';
import VenueSelector from '@/components/admin/VenueSelector';

interface BookingManagementProps {
  userRole: string | null;
  adminVenues: { venue_id: string }[];
  user?: { id: string }; // Add user prop for security validation
}

const BookingManagement: React.FC<BookingManagementProps> = ({ userRole, adminVenues, user }) => {
  const [bookings, setBookings] = useState<GroupedBooking[]>([]);
  const [venues, setVenues] = useState<{ id: string; name: string }[]>([]);
  const [selectedVenueId, setSelectedVenueId] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | BookingStatus>('all');
  const [paymentFilter, setPaymentFilter] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<'all' | 'cash' | 'online' | 'card' | 'free'>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Fetch venues for the selector
  useEffect(() => {
    const fetchVenues = async () => {
      if (userRole === 'admin' && adminVenues.length > 0) {
        const venueIds = adminVenues.map(v => v.venue_id);
        const { data: venueDetails, error } = await supabase
          .from('venues')
          .select('id, name')
          .in('id', venueIds)
          .eq('is_active', true)
          .order('name');

        if (!error) {
          setVenues(venueDetails || []);
          // Set default to first venue if only one venue
          if (venueDetails && venueDetails.length === 1) {
            setSelectedVenueId(venueDetails[0].id);
          }
        }
      } else if (userRole === 'super_admin') {
        const { data: allVenues, error } = await supabase
          .from('venues')
          .select('id, name')
          .eq('is_active', true)
          .order('name');

        if (!error) {
          setVenues(allVenues || []);
        }
      }
    };

    fetchVenues();
  }, [userRole, adminVenues]);

  useEffect(() => {
    fetchBookings();
  }, [filter, paymentFilter, paymentMethodFilter, searchQuery, userRole, adminVenues, selectedVenueId]);

  const fetchBookings = async () => {
    setLoading(true);
    try {
      console.log('🔒 Admin: Fetching booking groups and individual bookings for role:', userRole);

      // Step 1: Fetch booking groups with admin venue filtering
      let groupQuery = supabase
        .from('booking_groups_detailed')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply venue filtering for booking groups
      if (userRole === 'admin' && adminVenues.length > 0) {
        const venueIds = adminVenues.map(v => v.venue_id);
        groupQuery = groupQuery.in('venue_id', venueIds);
      }

      if (selectedVenueId && selectedVenueId !== 'all') {
        groupQuery = groupQuery.eq('venue_id', selectedVenueId);
      }

      const { data: groupData, error: groupError } = await groupQuery;

      if (groupError) {
        console.error('Booking groups error:', groupError);
        throw groupError;
      }

      // Step 2: Fetch individual bookings not part of any group with admin filtering
      let individualQuery = supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          total_price,
          status,
          booking_reference,
          payment_reference,
          payment_status,
          payment_method,
          user_id,
          guest_name,
          guest_phone,
          created_at,
          booked_by_admin_id,
          cancellation_reason,
          booking_group_id,
          court:courts!inner (
            id,
            name,
            venue_id,
            venue:venues!inner (
              id,
              name
            ),
            sport:sports (
              id,
              name
            )
          ),
          admin_booking:admin_bookings(
            id,
            booking_id,
            admin_id,
            customer_name,
            customer_phone,
            payment_method,
            payment_status,
            amount_collected,
            created_at,
            notes
          )
        `)
        .is('booking_group_id', null)
        .order('booking_date', { ascending: false })
        .order('created_at', { ascending: false });

      // SECURITY & PERFORMANCE: Apply venue filtering for individual bookings
      if (userRole === 'admin' && adminVenues.length > 0) {
        const venueIds = adminVenues.map(v => v.venue_id);
        individualQuery = individualQuery.in('court.venue.id', venueIds);
      }

      if (selectedVenueId && selectedVenueId !== 'all') {
        individualQuery = individualQuery.eq('court.venue.id', selectedVenueId);
      }

      // Apply filters to individual bookings
      if (filter !== 'all') {
        individualQuery = individualQuery.eq('status', filter);
      }

      if (paymentFilter !== 'all') {
        individualQuery = individualQuery.eq('payment_status', paymentFilter);
      }

      if (paymentMethodFilter !== 'all') {
        individualQuery = individualQuery.eq('payment_method', paymentMethodFilter);
      }

      // Add search functionality for individual booking references
      if (searchQuery.trim()) {
        const trimmedQuery = searchQuery.trim().toUpperCase();
        if (trimmedQuery.startsWith('GR2P-')) {
          individualQuery = individualQuery.ilike('booking_reference', `%${trimmedQuery}%`);
        } else {
          individualQuery = individualQuery.ilike('booking_reference', `%GR2P-${trimmedQuery}%`);
        }
      }

      const { data: individualData, error: individualError } = await individualQuery;

      if (individualError) {
        console.error('Individual bookings error:', individualError);
        throw individualError;
      }

      // Step 3: Transform booking groups to GroupedBooking interface
      const transformedGroups = (groupData || []).map(group => {
        const individualBookings = group.individual_bookings || [];
        const calculatedTotal = individualBookings.length > 0
          ? individualBookings.reduce((sum: number, booking: any) => sum + (booking.total_price || 0), 0)
          : parseFloat(group.total_amount || 0);

        return {
          id: group.id,
          booking_date: group.booking_date,
          start_time: group.earliest_start_time,
          end_time: group.latest_end_time,
          total_price: Math.max(calculatedTotal, parseFloat(group.total_amount || 0)),
          status: group.status,
          booking_reference: group.group_reference,
          payment_reference: group.payment_reference,
          payment_status: group.payment_status,
          payment_method: group.payment_method,
          created_at: group.created_at,
          guest_name: group.guest_name,
          user_id: group.user_id, // For admin interface
          booked_by_admin_id: null, // Booking groups don't have admin bookers
          cancellation_reason: null, // Handle at individual level
          court: {
            id: group.court_id,
            name: group.court_name,
            venue: {
              name: group.venue_name,
              id: group.venue_id,
              location: group.venue_location
            },
            sport: {
              name: group.sport_name,
              id: group.sport_id
            }
          },
          slot_count: group.booking_count,
          individual_bookings: individualBookings,
          cancellations: [], // TODO: Handle group cancellations
          is_booking_group: true,
          admin_booking: null, // Booking groups don't have admin booking info
          user_info: null, // Will be populated below
          admin_info: null
        };
      });

      // Step 4: Transform individual bookings to GroupedBooking interface
      const transformedIndividuals = (individualData || [])
        .filter(booking => booking.court && booking.court.venue && booking.court.sport)
        .map(booking => ({
          id: booking.id,
          booking_date: booking.booking_date,
          start_time: booking.start_time,
          end_time: booking.end_time,
          total_price: booking.total_price,
          status: booking.status as BookingStatus,
          booking_reference: booking.booking_reference,
          payment_reference: booking.payment_reference,
          payment_status: booking.payment_status,
          payment_method: booking.payment_method,
          created_at: booking.created_at,
          guest_name: booking.guest_name,
          user_id: booking.user_id,
          booked_by_admin_id: booking.booked_by_admin_id,
          cancellation_reason: booking.cancellation_reason,
          court: {
            id: booking.court.id,
            name: booking.court.name,
            venue: {
              name: booking.court.venue.name,
              id: booking.court.venue.id,
              location: booking.court.venue.location || ''
            },
            sport: {
              name: booking.court.sport.name,
              id: booking.court.sport.id
            }
          },
          slot_count: 1,
          individual_bookings: [{
            id: booking.id,
            start_time: booking.start_time,
            end_time: booking.end_time,
            total_price: booking.total_price,
            status: booking.status,
            cancellation_reason: booking.cancellation_reason
          }],
          cancellations: [],
          is_booking_group: false,
          admin_booking: booking.admin_booking && Array.isArray(booking.admin_booking) && booking.admin_booking.length > 0
            ? booking.admin_booking[0] as AdminBookingInfo
            : null,
          user_info: null, // Will be populated below
          admin_info: null
        }));

      // Step 5: Group individual bookings using client-side logic
      const individualGrouped = groupConsecutiveBookings(transformedIndividuals);

      // Step 6: Combine all bookings
      const allBookings = [...transformedGroups, ...individualGrouped]
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Step 7: Apply filters to combined results
      let filteredBookings = allBookings;

      // Apply booking group filters
      if (filter !== 'all') {
        filteredBookings = filteredBookings.filter(booking => booking.status === filter);
      }

      if (paymentFilter !== 'all') {
        filteredBookings = filteredBookings.filter(booking => booking.payment_status === paymentFilter);
      }

      if (paymentMethodFilter !== 'all') {
        filteredBookings = filteredBookings.filter(booking => booking.payment_method === paymentMethodFilter);
      }

      if (searchQuery.trim()) {
        const trimmedQuery = searchQuery.trim().toUpperCase();
        filteredBookings = filteredBookings.filter(booking => {
          const ref = booking.booking_reference || '';
          return ref.toUpperCase().includes(trimmedQuery) ||
                 ref.toUpperCase().includes(`GR2P-${trimmedQuery}`);
        });
      }

      // Step 8: Batch fetch user and admin profiles for all bookings
      if (filteredBookings.length > 0) {
        const userIds = [...new Set(filteredBookings.map(b => b.user_id).filter(Boolean))];
        const adminIds = [...new Set(filteredBookings.map(b => b.booked_by_admin_id).filter(Boolean))];

        const userProfilesPromise = userIds.length > 0
          ? supabase.from('profiles').select('id, full_name, phone').in('id', userIds)
          : Promise.resolve({ data: [], error: null });

        const adminProfilesPromise = adminIds.length > 0
          ? supabase.from('profiles').select('id, full_name').in('id', adminIds)
          : Promise.resolve({ data: [], error: null });

        const [userProfilesResult, adminProfilesResult] = await Promise.all([
          userProfilesPromise,
          adminProfilesPromise
        ]);

        const userProfileMap = new Map();
        const adminProfileMap = new Map();

        userProfilesResult.data?.forEach(profile => {
          userProfileMap.set(profile.id, profile);
        });

        adminProfilesResult.data?.forEach(profile => {
          adminProfileMap.set(profile.id, profile);
        });

        // Add profile data to bookings
        filteredBookings.forEach(booking => {
          if (booking.user_id && userProfileMap.has(booking.user_id)) {
            const userProfile = userProfileMap.get(booking.user_id);
            booking.user_info = {
              full_name: userProfile.full_name,
              phone: userProfile.phone,
              email: null
            };
          }

          if (booking.booked_by_admin_id && adminProfileMap.has(booking.booked_by_admin_id)) {
            const adminProfile = adminProfileMap.get(booking.booked_by_admin_id);
            booking.admin_info = {
              full_name: adminProfile.full_name,
              email: null
            };
          }
        });
      }

      setBookings(filteredBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load bookings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const updateBookingStatus = async (bookingId: string, status: BookingStatus, cancellationReason?: string) => {
    try {
      // Security check: Only allow cancellation and completion
      if (status !== 'cancelled' && status !== 'completed') {
        throw new Error('Invalid status change. Only cancellation and completion are allowed.');
      }

      if (!user?.id) {
        throw new Error('User authentication required');
      }

      // ✅ SECURITY ENHANCEMENT: Use protected booking update with role validation
      await protectedBookingUpdate(
        bookingId,
        status,
        {
          userId: user.id,
          fallbackToUnsafe: true, // Maintain backwards compatibility
          logOnly: true, // Only log security events for now
        },
        cancellationReason
      );

      // Log the security-validated operation
      logSecurityEvent('DESKTOP_BOOKING_STATUS_UPDATE', user.id, {
        bookingId,
        status,
        cancellationReason,
        userRole
      });

      toast({
        title: 'Status Updated',
        description: status === 'cancelled'
          ? `Booking has been cancelled. Reason: ${cancellationReason}`
          : `Booking has been marked as ${status}`,
      });

      fetchBookings();
    } catch (error) {
      console.error('Error updating booking:', error);
      toast({
        title: 'Error',
        description: 'Failed to update booking status',
        variant: 'destructive',
      });
      throw error; // Re-throw to handle in the modal
    }
  };

  return (
    <div>
      <Tabs defaultValue="bookings">
        <TabsList className="mb-6">
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="admin-booking">Book for Customer</TabsTrigger>
          <TabsTrigger value="slot-blocking">Block Time Slots</TabsTrigger>
        </TabsList>
        
        <TabsContent value="bookings">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Booking Management</h2>

            <div className="flex space-x-2">
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1 text-sm rounded-md ${
                  filter === 'all' 
                    ? 'bg-sport-green text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                All
              </button>
              <button
                onClick={() => setFilter('confirmed')}
                className={`px-3 py-1 text-sm rounded-md ${
                  filter === 'confirmed' 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Confirmed
              </button>
              <button
                onClick={() => setFilter('cancelled')}
                className={`px-3 py-1 text-sm rounded-md ${
                  filter === 'cancelled' 
                    ? 'bg-red-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Cancelled
              </button>
              <button
                onClick={() => setFilter('completed')}
                className={`px-3 py-1 text-sm rounded-md ${
                  filter === 'completed' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Completed
              </button>
              <button
                onClick={() => setFilter('pending')}
                className={`px-3 py-1 text-sm rounded-md ${
                  filter === 'pending' 
                    ? 'bg-yellow-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Pending
              </button>
            </div>
          </div>

          {/* Venue Selector */}
          {venues.length > 0 && (
            <div className="mb-6">
              <VenueSelector
                venues={venues}
                selectedVenueId={selectedVenueId}
                onVenueChange={setSelectedVenueId}
                userRole={userRole}
                variant="desktop"
                label="Filter by Venue"
                showAllOption={userRole === 'super_admin' || venues.length > 1}
                className="max-w-xs"
              />
            </div>
          )}

          {/* Search by Booking Reference */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Search by Booking Reference:</h3>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                placeholder="Enter booking reference (e.g., GR2P-1431D93D or 1431D93D)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-sport-green focus:border-transparent w-80"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="px-3 py-2 bg-gray-500 text-white rounded-md text-sm hover:bg-gray-600 transition-colors"
                >
                  Clear
                </button>
              )}
            </div>
          </div>

          {/* Payment filter controls */}
          <div className="mb-3">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Filter by Payment Status:</h3>
            <div className="flex space-x-2">
              <button
                onClick={() => setPaymentFilter('all')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentFilter === 'all' 
                    ? 'bg-sport-green text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                All Payments
              </button>
              <button
                onClick={() => setPaymentFilter('completed')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentFilter === 'completed' 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Completed
              </button>
              <button
                onClick={() => setPaymentFilter('pending')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentFilter === 'pending' 
                    ? 'bg-yellow-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Pending
              </button>
              <button
                onClick={() => setPaymentFilter('failed')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentFilter === 'failed' 
                    ? 'bg-red-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Failed
              </button>
            </div>
          </div>

          {/* Payment method filter controls */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Filter by Payment Method:</h3>
            <div className="flex space-x-2">
              <button
                onClick={() => setPaymentMethodFilter('all')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentMethodFilter === 'all' 
                    ? 'bg-sport-green text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                All Methods
              </button>
              <button
                onClick={() => setPaymentMethodFilter('cash')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentMethodFilter === 'cash' 
                    ? 'bg-green-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Cash
              </button>
              <button
                onClick={() => setPaymentMethodFilter('online')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentMethodFilter === 'online' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Online
              </button>
              <button
                onClick={() => setPaymentMethodFilter('card')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentMethodFilter === 'card' 
                    ? 'bg-purple-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Card
              </button>
              <button
                onClick={() => setPaymentMethodFilter('free')}
                className={`px-3 py-1 text-xs rounded-md ${
                  paymentMethodFilter === 'free' 
                    ? 'bg-gray-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                Free
              </button>
            </div>
          </div>
          
          <BookingsList 
            bookings={bookings} 
            isLoading={loading} 
            onStatusUpdate={updateBookingStatus} 
          />
        </TabsContent>
        
        <TabsContent value="admin-booking">
          <AdminBookingTab userRole={userRole} adminVenues={adminVenues} />
        </TabsContent>
        
        <TabsContent value="slot-blocking">
          <SlotBlockingTab userRole={userRole} adminVenues={adminVenues} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BookingManagement;
