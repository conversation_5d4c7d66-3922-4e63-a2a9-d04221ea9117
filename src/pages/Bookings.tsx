import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Calendar, Clock, MapPin, ChevronRight, ArrowLeft, Loader2, Eye } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { groupConsecutiveBookings, GroupedBooking } from '@/utils/bookingGrouping';
import { formatBookingGroupTime, calculateBookingGroupTotalPrice } from '@/utils/bookingGroupTimeFormatting';
import BookingDetailsModal from '@/components/BookingDetailsModal';
import RefundInfo from '@/components/RefundInfo';

// Using GroupedBooking interface from utils/bookingGrouping.ts for consistency

const Bookings: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [upcomingBookings, setUpcomingBookings] = useState<GroupedBooking[]>([]);
  const [pastBookings, setPastBookings] = useState<GroupedBooking[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBooking, setSelectedBooking] = useState<GroupedBooking | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (user) {
      fetchBookings();
    }
  }, [user]);

  const fetchBookings = async () => {
    setLoading(true);
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayStr = today.toISOString().split('T')[0];

      // First, fetch booking groups for better UX (same as Profile page)
      const { data: groupData, error: groupError } = await supabase
        .from('booking_groups_detailed')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (groupError) {
        console.error('Booking groups error:', groupError);
        throw groupError;
      }

      // Also fetch individual bookings not part of any group
      const { data: individualData, error: individualError } = await supabase
        .from('bookings')
        .select(`
          id,
          booking_date,
          start_time,
          end_time,
          total_price,
          status,
          booking_reference,
          payment_reference,
          payment_status,
          payment_method,
          created_at,
          guest_name,
          booking_group_id,
          court:courts (
            name,
            venue:venues (name, location, id),
            sport:sports (name, id)
          ),
          cancellations (
            id,
            refund_status,
            refund_notes,
            refund_amount,
            refund_processed_at,
            cancellation_reason
          )
        `)
        .eq('user_id', user?.id)
        .is('booking_group_id', null)
        .order('booking_date', { ascending: false })
        .order('created_at', { ascending: false });

      if (individualError) {
        console.error('Individual bookings error:', individualError);
        throw individualError;
      }

      // Transform booking groups to match GroupedBooking interface (same as Profile page)
      const transformedGroups = (groupData || []).map(group => {
        // Calculate correct total price from individual bookings if available
        const individualBookings = group.individual_bookings || [];
        const calculatedTotal = individualBookings.length > 0
          ? individualBookings.reduce((sum: number, booking: any) => sum + (booking.total_price || 0), 0)
          : parseFloat(group.total_amount || 0);

        return {
          id: group.id,
          booking_date: group.booking_date,
          start_time: group.earliest_start_time,
          end_time: group.latest_end_time,
          total_price: Math.max(calculatedTotal, parseFloat(group.total_amount || 0)),
          status: group.status,
          booking_reference: group.group_reference,
          payment_reference: group.payment_reference,
          payment_status: group.payment_status,
          payment_method: group.payment_method,
          created_at: group.created_at,
          guest_name: group.guest_name,
          court: {
            name: group.court_name,
            venue: {
              name: group.venue_name,
              id: group.venue_id,
              location: group.venue_location
            },
            sport: {
              name: group.sport_name,
              id: group.sport_id
            }
          },
          slot_count: group.booking_count,
          individual_bookings: individualBookings,
          cancellations: [], // TODO: Handle group cancellations
          is_booking_group: true
        };
      });

      // Transform individual bookings
      const validIndividualBookings = (individualData || [])
        .filter(booking => booking.court && booking.court.venue && booking.court.sport)
        .map(transformBooking);

      // Use database booking groups instead of client-side grouping
      const individualGrouped = groupConsecutiveBookings(validIndividualBookings);
      const allGroupedBookings = [...transformedGroups, ...individualGrouped]
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Split into upcoming and past bookings
      const upcomingBookings = allGroupedBookings.filter(booking =>
        booking.booking_date >= todayStr && ['pending', 'confirmed'].includes(booking.status)
      ).sort((a, b) => new Date(a.booking_date).getTime() - new Date(b.booking_date).getTime());

      const pastBookings = allGroupedBookings.filter(booking =>
        booking.booking_date < todayStr || ['cancelled', 'completed'].includes(booking.status)
      ).sort((a, b) => new Date(b.booking_date).getTime() - new Date(a.booking_date).getTime());

      setUpcomingBookings(upcomingBookings.slice(0, 10)); // Limit to 10 for performance
      setPastBookings(pastBookings.slice(0, 10)); // Limit to 10 for performance
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load bookings',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const transformBooking = (booking: any): GroupedBooking => ({
    id: booking.id,
    booking_date: booking.booking_date,
    start_time: booking.start_time,
    end_time: booking.end_time,
    total_price: booking.total_price,
    status: booking.status,
    booking_reference: booking.booking_reference,
    payment_reference: booking.payment_reference,
    payment_status: booking.payment_status,
    payment_method: booking.payment_method,
    created_at: booking.created_at,
    guest_name: booking.guest_name,
    court: {
      name: booking.court?.name || 'N/A',
      venue: {
        name: booking.court?.venue?.name || 'N/A',
        location: booking.court?.venue?.location || 'N/A',
        id: booking.court?.venue?.id || ''
      },
      sport: {
        name: booking.court?.sport?.name || 'N/A',
        id: booking.court?.sport?.id || ''
      }
    },
    slot_count: 1, // Individual bookings have 1 slot
    individual_bookings: [{
      id: booking.id,
      start_time: booking.start_time,
      end_time: booking.end_time,
      total_price: booking.total_price,
      status: booking.status,
      cancellation_reason: booking.cancellation_reason
    }],
    cancellations: booking.cancellations || [],
    is_booking_group: false
  });

  const handleViewDetails = (booking: GroupedBooking) => {
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-500/20 text-green-400';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'cancelled':
        return 'bg-red-500/20 text-red-400';
      case 'completed':
        return 'bg-blue-500/20 text-blue-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-navy-dark to-indigo/30">
      <Header />

      <main className="pt-12 pb-8 sm:pt-24 sm:pb-16 w-full flex-1 flex flex-col justify-center items-center sm:block">
        <div className="container mx-auto px-2 sm:px-4 mt-8 sm:mt-0">
          <div className="w-full mx-0 sm:max-w-4xl sm:mx-auto">
            {/* Back Button and Header */}
            <div className="mb-6 sm:mb-8">
              <button 
                onClick={() => navigate('/')} 
                className="flex items-center text-gray-300 hover:text-white transition-colors mb-6"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Home
              </button>

              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
                <h1 className="text-xl sm:text-3xl font-bold text-white">My Bookings</h1>
                <button 
                  onClick={() => navigate('/venues')} 
                  className="px-3 py-2 sm:px-4 sm:py-2 bg-gradient-to-r from-indigo to-indigo-dark text-white rounded-md hover:from-indigo-dark hover:to-indigo transition-all font-medium flex items-center transform hover:scale-[1.02] shadow-md sm:shadow-lg text-sm sm:text-base"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Book New Slot
                </button>
              </div>
            </div>

            {loading ? (
              <div className="flex justify-center items-center min-h-[300px]">
                <Loader2 className="h-8 w-8 animate-spin text-indigo-400" />
              </div>
            ) : (
              <div className="space-y-8">
                {/* Upcoming Bookings */}
                <section>
                  <h2 className="text-lg sm:text-xl font-semibold text-white mb-4 sm:mb-6 pb-2 border-b border-white/20">
                    Upcoming Bookings
                  </h2>
                  
                  {upcomingBookings.length === 0 ? (
                    <div className="backdrop-blur-sm bg-white/10 rounded-xl p-4 sm:p-6 text-center border border-white/20">
                      <p className="text-gray-300">No upcoming bookings found</p>
                      <button 
                        onClick={() => navigate('/venues')} 
                        className="mt-4 text-indigo-400 hover:text-white font-medium transition-colors"
                      >
                        Book your first slot
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-3 sm:space-y-4">
                      {upcomingBookings.map(booking => (
                        <div 
                          key={booking.id} 
                          className="backdrop-blur-sm bg-white/10 rounded-xl overflow-hidden border border-white/20 hover:shadow-lg transition-all"
                        >
                          <div className="p-4 sm:p-6">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4">
                              <div>
                                <h3 className="text-base sm:text-lg font-medium text-white flex items-center gap-2">
                                  {booking.court.venue.name} - {booking.court.name}
                                  {booking.slot_count > 1 && (
                                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-emerald-500/20 text-emerald-400">
                                      {booking.slot_count} slots
                                    </span>
                                  )}
                                </h3>
                                <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 mt-1">
                                  <p className="text-xs sm:text-sm text-gray-300">
                                    <span className="capitalize">{booking.court.sport.name.toLowerCase()}</span>
                                  </p>
                                  {booking.booking_reference && (
                                    <p className="text-xs text-indigo-400 font-mono">
                                      Ref: {booking.booking_reference}
                                    </p>
                                  )}
                                </div>
                              </div>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                                {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                              </span>
                            </div>

                            <div className="mt-3 sm:mt-4 grid grid-cols-1 md:grid-cols-3 gap-2 sm:gap-4">
                              <div className="flex items-center text-xs sm:text-base text-gray-200">
                                <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                                <span>{formatDate(booking.booking_date)}</span>
                              </div>
                              <div className="flex items-center text-xs sm:text-base text-gray-200">
                                <Clock className="w-4 h-4 mr-2 text-gray-400" />
                                <span>
                                  {formatBookingGroupTime(booking)}
                                </span>
                              </div>
                              <div className="flex items-center text-xs sm:text-base text-gray-200">
                                <span className="font-medium">₹{calculateBookingGroupTotalPrice(booking).toFixed(2)}</span>
                              </div>
                            </div>

                            <div className="mt-3 sm:mt-4 flex items-center justify-between">
                              <div className="flex items-center text-xs sm:text-sm text-gray-300">
                                <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                                <span>{booking.court.venue.location}</span>
                              </div>
                              <button
                                onClick={() => handleViewDetails(booking)}
                                className="inline-flex items-center px-3 py-1.5 bg-emerald-500/20 hover:bg-emerald-500/30 text-emerald-400 rounded-lg text-sm font-medium transition-colors"
                              >
                                <Eye className="w-4 h-4 mr-1" />
                                View Details
                              </button>
                            </div>

                            {/* Refund Information for cancelled bookings */}
                            {booking.status === 'cancelled' && booking.cancellations && booking.cancellations.length > 0 && (
                              <div className="mt-4">
                                <RefundInfo
                                  cancellation={booking.cancellations[0]}
                                  bookingAmount={calculateBookingGroupTotalPrice(booking)}
                                  className="bg-white/5 border-white/10"
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </section>

                {/* Past Bookings */}
                <section>
                  <h2 className="text-lg sm:text-xl font-semibold text-white mb-4 sm:mb-6 pb-2 border-b border-white/20">
                    Past Bookings
                  </h2>
                  
                  {pastBookings.length === 0 ? (
                    <div className="backdrop-blur-sm bg-white/10 rounded-xl p-4 sm:p-6 text-center border border-white/20">
                      <p className="text-gray-300">No past bookings found</p>
                    </div>
                  ) : (
                    <div className="backdrop-blur-sm bg-white/10 rounded-xl overflow-hidden border border-white/20">
                      <div className="overflow-x-auto">
                        <table className="w-full text-xs sm:text-base">
                          <thead className="bg-white/10">
                            <tr>
                              <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-300">Date</th>
                              <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-300">Venue & Court</th>
                              <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-300">Reference</th>
                              <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-300">Time</th>
                              <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-300">Price</th>
                              <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-300">Status</th>
                              <th className="text-left py-2 px-2 sm:py-3 sm:px-4 font-medium text-gray-300">Actions</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-white/20">
                            {pastBookings.map(booking => (
                              <React.Fragment key={booking.id}>
                                <tr className="hover:bg-white/5 transition-colors">
                                  <td className="py-2 px-2 sm:py-3 sm:px-4 text-gray-200">
                                    {formatDate(booking.booking_date)}
                                  </td>
                                  <td className="py-2 px-2 sm:py-3 sm:px-4 text-gray-200">
                                    <p className="font-medium">{booking.court.venue.name}</p>
                                    <p className="text-xs sm:text-sm text-gray-300">
                                      {booking.court.name}
                                      {booking.slot_count > 1 && (
                                        <span className="ml-2 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-emerald-500/20 text-emerald-400">
                                          {booking.slot_count}x
                                        </span>
                                      )}
                                    </p>
                                  </td>
                                  <td className="py-2 px-2 sm:py-3 sm:px-4 text-indigo-400 font-mono text-xs">
                                    {booking.booking_reference || 'N/A'}
                                  </td>
                                  <td className="py-2 px-2 sm:py-3 sm:px-4 text-gray-200">
                                    {formatBookingGroupTime(booking)}
                                  </td>
                                  <td className="py-2 px-2 sm:py-3 sm:px-4 text-gray-200">
                                    ₹{calculateBookingGroupTotalPrice(booking).toFixed(2)}
                                  </td>
                                  <td className="py-2 px-2 sm:py-3 sm:px-4">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                                      {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                                    </span>
                                  </td>
                                  <td className="py-2 px-2 sm:py-3 sm:px-4">
                                    <button
                                      onClick={() => handleViewDetails(booking)}
                                      className="inline-flex items-center px-2 py-1 bg-emerald-500/20 hover:bg-emerald-500/30 text-emerald-400 rounded text-xs font-medium transition-colors"
                                    >
                                      <Eye className="w-3 h-3 mr-1" />
                                      Details
                                    </button>
                                  </td>
                                </tr>

                                {/* Refund Information Row for cancelled bookings */}
                                {booking.status === 'cancelled' && booking.cancellations && booking.cancellations.length > 0 && (
                                  <tr>
                                    <td colSpan={7} className="py-2 px-2 sm:py-3 sm:px-4">
                                      <RefundInfo
                                        cancellation={booking.cancellations[0]}
                                        bookingAmount={calculateBookingGroupTotalPrice(booking)}
                                        className="bg-white/5 border-white/10"
                                      />
                                    </td>
                                  </tr>
                                )}
                              </React.Fragment>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </section>
              </div>
            )}
          </div>
        </div>
      </main>

      <footer className="bg-navy-dark/50 backdrop-blur-sm py-6">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-400">&copy; {new Date().getFullYear()} Grid2Play. All rights reserved.</p>
        </div>
      </footer>

      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedBooking(null);
        }}
      />
    </div>
  );
};

export default Bookings;
