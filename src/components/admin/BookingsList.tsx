
import React, { useState } from 'react';
import { Book<PERSON>heck, BookX, CreditCard, DollarSign } from 'lucide-react';
import { Booking, BookingStatus } from '@/types/help';
import SportDisplayName from '@/components/SportDisplayName';
import { groupConsecutiveBookings, GroupedBooking } from '@/utils/bookingGrouping';
import { formatBookingGroupTime, calculateBookingGroupTotalPrice } from '@/utils/bookingGroupTimeFormatting';
import CancellationReasonModal from '../CancellationReasonModal';

interface BookingsListProps {
  bookings: GroupedBooking[];
  isLoading: boolean;
  onStatusUpdate: (bookingId: string, status: BookingStatus, cancellationReason?: string) => Promise<void>;
}

const getPaymentStatusColor = (status: string | null) => {
  if (!status) return 'bg-gray-100 text-gray-800';
  switch (status.toLowerCase()) {
    case 'completed': return 'bg-green-100 text-green-800';
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'failed': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getPaymentMethodIcon = (method: string | null) => {
  if (!method) return null;
  switch (method.toLowerCase()) {
    case 'cash': return <DollarSign className="w-4 h-4 mr-1" />;
    case 'card':
    case 'online': return <CreditCard className="w-4 h-4 mr-1" />;
    default: return null;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'short', day: 'numeric', month: 'short', year: 'numeric'
  });
};

const formatTime = (timeString: string) => {
  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const hour12 = hour % 12 || 12;
  return `${hour12}:${minutes} ${ampm}`;
};

const BookingsList: React.FC<BookingsListProps> = ({ bookings, isLoading, onStatusUpdate }) => {
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [bookingToCancel, setBookingToCancel] = useState<{
    id: string;
    info: {
      venueName: string;
      courtName: string;
      date: string;
      time: string;
      customerName?: string;
    };
  } | null>(null);
  const [cancelling, setCancelling] = useState(false);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-sport-green"></div>
      </div>
    );
  }
  
  if (bookings.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg">
        <p className="text-gray-600">No bookings found</p>
      </div>
    );
  }

  // Handle cancellation request
  const handleCancelRequest = (bookingId: string, booking: GroupedBooking) => {
    const customerName = booking.admin_booking?.customer_name ||
                        booking.guest_name ||
                        booking.user_info?.full_name ||
                        'Unknown Customer';

    setBookingToCancel({
      id: bookingId,
      info: {
        venueName: booking.court.venue.name,
        courtName: booking.court.name,
        date: formatDate(booking.booking_date),
        time: formatBookingGroupTime(booking),
        customerName
      }
    });
    setShowCancellationModal(true);
  };

  // Handle cancellation confirmation
  const handleCancelConfirm = async (reason: string) => {
    if (!bookingToCancel) return;

    setCancelling(true);
    try {
      await onStatusUpdate(bookingToCancel.id, 'cancelled', reason);
      setShowCancellationModal(false);
      setBookingToCancel(null);
    } catch (error) {
      console.error('Error cancelling booking:', error);
      // Keep modal open on error so user can retry
      // Error handling will be done by the parent component
    } finally {
      setCancelling(false);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    if (!cancelling) {
      setShowCancellationModal(false);
      setBookingToCancel(null);
    }
  };

  // Group consecutive bookings for display
  const groupedBookings = groupConsecutiveBookings(bookings);

  return (
    <div className="flex flex-col gap-2">
      {groupedBookings.map(booking => (
        <div key={booking.id} className="rounded-lg bg-navy-900 shadow border border-navy-700 px-3 py-2 flex flex-col text-xs text-white">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-white text-sm">{formatDate(booking.booking_date)}</span>
              {booking.slot_count > 1 && (
                <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-emerald-500/20 text-emerald-400">
                  {booking.slot_count} slots
                </span>
              )}
            </div>
            <span className="text-gray-300">{formatBookingGroupTime(booking)}</span>
          </div>
          <div className="mb-1">
            <span className="font-medium text-white truncate block">{booking.court.venue.name}</span>
            <span className="text-gray-400 truncate block">{booking.court.name} / <SportDisplayName venueId={booking.court.venue.id} sportId={booking.court.sport.id} defaultName={booking.court.sport.name} /></span>
          </div>
          <div className="mb-1">
            {booking.admin_booking ? (
              <div>
                <span className="font-medium text-purple-300">{booking.admin_booking.customer_name} <span className="text-xs text-purple-400">(Admin)</span></span>
                {booking.admin_booking.customer_phone && (
                  <div className="text-xs text-purple-200">📞 {booking.admin_booking.customer_phone}</div>
                )}
              </div>
            ) : booking.guest_name ? (
              <div>
                <span className="font-medium text-white">{booking.guest_name} <span className="text-xs text-gray-400">(Guest)</span></span>
                {booking.guest_phone && (
                  <div className="text-xs text-gray-300">📞 {booking.guest_phone}</div>
                )}
              </div>
            ) : booking.user_info ? (
              <div>
                <span className="font-medium text-white">{booking.user_info.full_name || 'User'}</span>
                {booking.user_info.phone && (
                  <div className="text-xs text-gray-300">📞 {booking.user_info.phone}</div>
                )}
              </div>
            ) : (
              <span className="text-gray-400">User ID: {booking.individual_bookings[0]?.id || 'No user'}</span>
            )}
          </div>
          <div className="flex items-center gap-2 mb-1">
            <span className="text-green-300 font-semibold">₹{calculateBookingGroupTotalPrice(booking).toFixed(2)}</span>
            <span className={`inline-flex items-center px-2 py-0.5 text-xs font-semibold rounded-full ${getPaymentStatusColor(booking.payment_status)}`}>{getPaymentMethodIcon(booking.payment_method)}{booking.payment_method ? booking.payment_method.charAt(0).toUpperCase() + booking.payment_method.slice(1) : 'Unknown'}</span>
            <span className={`inline-block px-2 py-0.5 rounded ${getPaymentStatusColor(booking.payment_status)}`}>{booking.payment_status || 'Unknown'}</span>
          </div>
          <div className="flex items-center gap-2 mb-1">
            <span className={`capitalize px-2 py-0.5 rounded text-xs font-semibold ${booking.status === 'confirmed' ? 'bg-green-900 text-green-300' : booking.status === 'cancelled' ? 'bg-red-900 text-red-300' : 'bg-navy-800 text-gray-300'}`}>{booking.status}</span>
            {booking.booking_reference && (
              <span className="text-indigo-400 font-mono text-xs font-semibold" title={booking.booking_reference}>{booking.booking_reference}</span>
            )}
            {booking.payment_reference && (
              <span className="text-gray-500 break-all text-xs" title={booking.payment_reference}>Pay: {booking.payment_reference}</span>
            )}
          </div>
          {/* Show cancellation reason if booking is cancelled */}
          {booking.status === 'cancelled' && booking.individual_bookings[0]?.cancellation_reason && (
            <div className="mb-2">
              <div className="bg-red-900/20 border border-red-800/30 rounded-md p-2.5 shadow-sm">
                <p className="text-xs text-red-300 font-semibold mb-1.5 flex items-center gap-1">
                  <span className="w-1.5 h-1.5 bg-red-400 rounded-full"></span>
                  Cancellation Reason:
                </p>
                <p className="text-xs text-red-200 leading-relaxed break-words">
                  {booking.individual_bookings[0].cancellation_reason}
                </p>
              </div>
            </div>
          )}
          <div className="flex gap-2 mt-1">
            {/* For grouped bookings, we'll update the first booking in the group */}
            {booking.individual_bookings.map((individualBooking, index) => (
              <div key={individualBooking.id} className="flex gap-2">
                {/* Only show Cancel button for non-cancelled bookings */}
                {booking.status !== 'cancelled' && (
                  <button
                    onClick={() => handleCancelRequest(individualBooking.id, booking)}
                    className="px-2 py-0.5 rounded bg-red-900 text-red-200 text-xs font-medium flex items-center gap-1"
                    title={booking.slot_count > 1 ? `Cancel slot ${index + 1}` : 'Cancel'}
                    disabled={cancelling}
                  >
                    <BookX className="w-3 h-3" />
                    {booking.slot_count > 1 ? `Slot ${index + 1}` : 'Cancel'}
                  </button>
                )}
                {/* Only show Complete button for confirmed bookings */}
                {booking.status === 'confirmed' && (
                  <button
                    onClick={async () => {
                      try {
                        await onStatusUpdate(individualBooking.id, 'completed');
                      } catch (error) {
                        console.error('Error completing booking:', error);
                        // Error handling will be done by the parent component
                      }
                    }}
                    className="px-2 py-0.5 rounded bg-blue-900 text-blue-200 text-xs font-medium flex items-center gap-1"
                    title={booking.slot_count > 1 ? `Complete slot ${index + 1}` : 'Mark as Completed'}
                    disabled={cancelling}
                  >
                    <BookCheck className="w-3 h-3" />
                    {booking.slot_count > 1 ? `Complete ${index + 1}` : 'Complete'}
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Cancellation Reason Modal */}
      <CancellationReasonModal
        isOpen={showCancellationModal}
        onClose={handleModalClose}
        onConfirm={handleCancelConfirm}
        bookingInfo={bookingToCancel?.info}
        loading={cancelling}
      />
    </div>
  );
};

export default BookingsList;
