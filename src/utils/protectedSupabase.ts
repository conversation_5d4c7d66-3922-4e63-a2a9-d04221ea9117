/**
 * Protected Supabase Operations - Secure Admin Query Wrappers
 * 
 * This module provides secure wrappers around Supabase operations with
 * built-in role validation and access control. All functions maintain
 * backwards compatibility and can be safely rolled back.
 * 
 * SAFETY: All operations fallback to existing behavior if validation fails
 */

import { supabase } from '@/integrations/supabase/client';
import { verifyAdminRole, verifyVenueAccess, logSecurityEvent, validateAdminOperation } from './adminSecurity';
import type { BookingStatus } from '@/types/help';

export interface ProtectedOperationOptions {
  userId: string;
  fallbackToUnsafe?: boolean; // If true, allows operation even if validation fails
  logOnly?: boolean; // If true, only logs security events but doesn't block
  venueId?: string; // For venue-specific operations
}

/**
 * Protected booking status update with role validation
 * 
 * @param bookingId - Booking ID to update
 * @param status - New status to set
 * @param options - Protection options
 * @param cancellationReason - Optional cancellation reason
 * @returns Supabase operation result
 */
export async function protectedBookingUpdate(
  bookingId: string,
  status: BookingStatus,
  options: ProtectedOperationOptions,
  cancellationReason?: string
) {
  const { userId, fallbackToUnsafe = true, logOnly = true } = options;

  try {
    // Step 1: Validate admin operation
    const validation = await validateAdminOperation(
      userId,
      `UPDATE_BOOKING_STATUS_${status.toUpperCase()}`,
      options.venueId
    );

    if (!validation.allowed) {
      if (logOnly || fallbackToUnsafe) {
        console.warn(`[SECURITY WARNING] ${validation.reason} - Proceeding with fallback`);
      } else {
        throw new Error(validation.reason || 'Operation not allowed');
      }
    }

    // Step 2: Check if this booking is part of a booking group
    const { data: bookingData, error: bookingError } = await supabase
      .from('bookings')
      .select('id, booking_group_id, booking_reference, status')
      .eq('id', bookingId);

    if (bookingError) {
      throw bookingError;
    }

    if (!bookingData || bookingData.length === 0) {
      throw new Error(`Booking not found or access denied: ${bookingId}. Please check your permissions and try logging in again.`);
    }

    const bookingInfo = bookingData[0];

    // Step 3: Prepare update data (existing logic)
    const updateData: { status: BookingStatus; cancellation_reason?: string } = { status };

    if (status === 'cancelled' && cancellationReason) {
      updateData.cancellation_reason = cancellationReason;
    }

    // Step 4: Execute the update (existing Supabase call)
    const { error } = await supabase
      .from('bookings')
      .update(updateData)
      .eq('id', bookingId);

    if (error) {
      // Provide more helpful error messages
      if (error.message.includes('Only admin and super_admin can cancel bookings')) {
        throw new Error('Permission denied: Only admin and super_admin users can cancel bookings. Please check your role and try again.');
      }

      if (error.message.includes('JWT')) {
        throw new Error('Authentication error: Your session may have expired. Please log out and log in again.');
      }

      throw error;
    }

    // Step 5: Handle booking group status update if applicable
    if (bookingInfo.booking_group_id && status === 'cancelled') {
      await handleBookingGroupStatusUpdate(bookingInfo.booking_group_id, status);
    }

    // Step 6: Log successful operation
    logSecurityEvent('BOOKING_STATUS_UPDATED', userId, {
      bookingId,
      status,
      cancellationReason,
      bookingGroupId: bookingInfo.booking_group_id,
      venueId: options.venueId
    });

    return { error: null };

  } catch (error) {
    console.error('Error in protectedBookingUpdate:', error);
    logSecurityEvent('BOOKING_UPDATE_FAILED', userId, {
      bookingId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle booking group status updates when individual bookings are cancelled
 */
async function handleBookingGroupStatusUpdate(bookingGroupId: string, status: BookingStatus) {
  try {
    // Get all bookings in the group
    const { data: groupBookings, error: groupError } = await supabase
      .from('bookings')
      .select('id, status')
      .eq('booking_group_id', bookingGroupId);

    if (groupError) throw groupError;

    if (!groupBookings || groupBookings.length === 0) return;

    // Check if all bookings in the group have the same status
    const allCancelled = groupBookings.every(booking => booking.status === 'cancelled');
    const allCompleted = groupBookings.every(booking => booking.status === 'completed');

    let newGroupStatus: BookingStatus | null = null;

    if (allCancelled) {
      newGroupStatus = 'cancelled';
    } else if (allCompleted) {
      newGroupStatus = 'completed';
    }

    // Update booking group status if needed
    if (newGroupStatus) {
      const { error: groupUpdateError } = await supabase
        .from('booking_groups')
        .update({ status: newGroupStatus })
        .eq('id', bookingGroupId);

      if (groupUpdateError) {
        console.error('Error updating booking group status:', groupUpdateError);
      } else {
        console.log(`Updated booking group ${bookingGroupId} status to ${newGroupStatus}`);
      }
    }

  } catch (error) {
    console.error('Error in handleBookingGroupStatusUpdate:', error);
    // Don't throw error to avoid breaking the main cancellation process
  }
}

/**
 * Protected booking query with sanitized profile data
 * Removes email addresses while keeping name and phone for legitimate business needs
 * 
 * @param options - Protection and query options
 * @returns Sanitized booking data
 */
export async function protectedBookingQuery(options: ProtectedOperationOptions & {
  filters?: {
    status?: string;
    paymentStatus?: string;
    paymentMethod?: string;
    venueIds?: string[];
  };
}) {
  const { userId, fallbackToUnsafe = true, logOnly = true } = options;

  try {
    // Step 1: Validate admin access
    const roleValidation = await verifyAdminRole(userId);

    if (!roleValidation.isValid) {
      if (logOnly || fallbackToUnsafe) {
        console.warn('[SECURITY WARNING] Invalid admin role - Proceeding with fallback');
      } else {
        throw new Error('Insufficient permissions for booking query');
      }
    }

    // Step 2: Build query with sanitized profile selection
    // IMPORTANT: Exclude email for privacy, keep name and phone for business needs
    let query = supabase
      .from('bookings')
      .select(`
        id,
        booking_date,
        start_time,
        end_time,
        total_price,
        status,
        payment_reference,
        payment_status,
        payment_method,
        user_id,
        guest_name,
        guest_phone,
        created_at,
        booked_by_admin_id,
        cancellation_reason,
        court:courts (
          id,
          name,
          venue:venues (
            id,
            name
          ),
          sport:sports (
            id,
            name
          )
        ),
        admin_booking:admin_bookings(
          id,
          booking_id,
          admin_id,
          customer_name,
          customer_phone,
          payment_method,
          payment_status,
          amount_collected,
          created_at,
          notes
        )
      `)
      .order('booking_date', { ascending: false })
      .order('created_at', { ascending: false });

    // Step 3: Apply filters
    if (options.filters) {
      const { status, paymentStatus, paymentMethod, venueIds } = options.filters;
      
      if (status && status !== 'all') {
        query = query.eq('status', status);
      }
      if (paymentStatus && paymentStatus !== 'all') {
        query = query.eq('payment_status', paymentStatus);
      }
      if (paymentMethod && paymentMethod !== 'all') {
        query = query.eq('payment_method', paymentMethod);
      }
      if (venueIds && venueIds.length > 0) {
        // Filter by venue access for regular admins
        const { data: courtIds, error: courtError } = await supabase
          .from('courts')
          .select('id')
          .in('venue_id', venueIds);
        
        if (courtError) throw courtError;
        
        if (courtIds && courtIds.length > 0) {
          const courtIdArray = courtIds.map(c => c.id);
          query = query.in('court_id', courtIdArray);
        } else {
          return { data: [], error: null };
        }
      }
    }

    // Step 4: Execute query
    const { data, error } = await query;
    if (error) throw error;

    // Step 5: Process bookings with sanitized user data
    const processedBookings = await Promise.all(
      data?.map(async booking => {
        let processedBooking: any = {
          ...booking,
          admin_booking: null,
          court: booking.court,
          status: booking.status,
        };

        // Handle admin_booking
        if (booking.admin_booking && Array.isArray(booking.admin_booking) && booking.admin_booking.length > 0) {
          processedBooking.admin_booking = booking.admin_booking[0];
        }

        // SANITIZED USER INFO: Only name and phone, NO EMAIL
        if (booking.user_id) {
          try {
            const { data: userData, error: userError } = await supabase
              .from('profiles')
              .select('full_name, phone') // ✅ REMOVED EMAIL for privacy
              .eq('id', booking.user_id)
              .maybeSingle();

            if (!userError && userData) {
              processedBooking.user_info = {
                full_name: userData.full_name,
                phone: userData.phone
                // ✅ EMAIL EXCLUDED for admin privacy compliance
              };
            }
          } catch (err) {
            console.error('Error fetching sanitized user info:', err);
          }
        }

        // Admin info (for bookings created by admins)
        if (booking.booked_by_admin_id) {
          try {
            const { data: adminData, error: adminError } = await supabase
              .from('profiles')
              .select('full_name') // Only name for admin info
              .eq('id', booking.booked_by_admin_id)
              .maybeSingle();

            if (!adminError && adminData) {
              processedBooking.admin_info = {
                full_name: adminData.full_name
              };
            }
          } catch (err) {
            console.error('Error fetching admin info:', err);
          }
        }

        return processedBooking;
      }) || []
    );

    // Step 6: Log successful query
    logSecurityEvent('PROTECTED_BOOKING_QUERY', userId, {
      resultCount: processedBookings.length,
      filters: options.filters
    });

    return { data: processedBookings, error: null };

  } catch (error) {
    console.error('Error in protectedBookingQuery:', error);
    logSecurityEvent('PROTECTED_QUERY_FAILED', userId, {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Protected venue operation wrapper
 * 
 * @param operation - Operation to perform
 * @param venueId - Venue ID for the operation
 * @param options - Protection options
 * @returns Operation result
 */
export async function protectedVenueOperation(
  operation: () => Promise<any>,
  venueId: string,
  options: ProtectedOperationOptions
) {
  const { userId, fallbackToUnsafe = true, logOnly = true } = options;

  try {
    // Validate venue access
    const venueAccess = await verifyVenueAccess(userId, venueId);

    if (!venueAccess.hasAccess) {
      if (logOnly || fallbackToUnsafe) {
        console.warn(`[SECURITY WARNING] No venue access - Proceeding with fallback`);
      } else {
        throw new Error('No access to this venue');
      }
    }

    // Execute the operation
    const result = await operation();

    logSecurityEvent('PROTECTED_VENUE_OPERATION', userId, {
      venueId,
      accessType: venueAccess.accessType
    });

    return result;

  } catch (error) {
    console.error('Error in protectedVenueOperation:', error);
    logSecurityEvent('PROTECTED_VENUE_OPERATION_FAILED', userId, {
      venueId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}
