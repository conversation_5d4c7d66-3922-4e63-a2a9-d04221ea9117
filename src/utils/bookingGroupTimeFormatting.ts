import { GroupedBooking } from './bookingGrouping';

/**
 * Formats time from 24-hour format to 12-hour format with AM/PM
 * @param time - Time string in HH:MM:SS or HH:MM format
 * @returns Formatted time string like "4:00 PM"
 */
export function formatTime(time: string): string {
  if (!time) return '';
  
  // Handle both HH:MM:SS and HH:MM formats
  const timeParts = time.split(':');
  const hours = parseInt(timeParts[0], 10);
  const minutes = parseInt(timeParts[1], 10);
  
  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
  const displayMinutes = minutes.toString().padStart(2, '0');
  
  return `${displayHours}:${displayMinutes} ${period}`;
}

/**
 * Groups consecutive time slots and formats them as time ranges
 * @param individualBookings - Array of individual booking objects with start_time and end_time
 * @returns Array of formatted time range strings
 */
export function groupConsecutiveTimeSlots(individualBookings: Array<{
  start_time: string;
  end_time: string;
  total_price?: number;
  status?: string;
}>): string[] {
  if (!individualBookings || individualBookings.length === 0) return [];

  // Sort bookings by start time
  const sortedBookings = [...individualBookings].sort((a, b) => 
    a.start_time.localeCompare(b.start_time)
  );

  const timeRanges: string[] = [];
  let currentRangeStart = sortedBookings[0].start_time;
  let currentRangeEnd = sortedBookings[0].end_time;

  for (let i = 1; i < sortedBookings.length; i++) {
    const currentBooking = sortedBookings[i];
    const previousEndTime = currentRangeEnd;
    
    // Check if current booking is consecutive to the previous range
    if (currentBooking.start_time === previousEndTime) {
      // Extend the current range
      currentRangeEnd = currentBooking.end_time;
    } else {
      // End current range and start a new one
      timeRanges.push(`${formatTime(currentRangeStart)} - ${formatTime(currentRangeEnd)}`);
      currentRangeStart = currentBooking.start_time;
      currentRangeEnd = currentBooking.end_time;
    }
  }

  // Add the final range
  timeRanges.push(`${formatTime(currentRangeStart)} - ${formatTime(currentRangeEnd)}`);

  return timeRanges;
}

/**
 * Formats booking group time display based on whether slots are continuous or non-continuous
 * @param booking - GroupedBooking object
 * @returns Formatted time string for display
 */
export function formatBookingGroupTime(booking: GroupedBooking): string {
  // For booking groups with individual_bookings data, use detailed formatting
  if (booking.is_booking_group && booking.individual_bookings && booking.individual_bookings.length > 0) {
    const timeRanges = groupConsecutiveTimeSlots(booking.individual_bookings);
    
    if (timeRanges.length === 1) {
      // All slots are continuous - show single range
      return timeRanges[0];
    } else {
      // Non-continuous slots - show separate ranges
      return timeRanges.join(', ');
    }
  }

  // Fallback to simple start-end time formatting for individual bookings or legacy data
  return `${formatTime(booking.start_time)} - ${formatTime(booking.end_time)}`;
}

/**
 * Gets a summary of booking time with slot count for display
 * @param booking - GroupedBooking object
 * @returns Formatted time summary with slot count
 */
export function getBookingTimeSummary(booking: GroupedBooking): string {
  const timeDisplay = formatBookingGroupTime(booking);
  
  if (booking.slot_count > 1) {
    return `${timeDisplay} (${booking.slot_count} slots)`;
  }
  
  return timeDisplay;
}

/**
 * Checks if booking group has non-continuous time slots
 * @param booking - GroupedBooking object
 * @returns True if slots are non-continuous, false otherwise
 */
export function hasNonContinuousSlots(booking: GroupedBooking): boolean {
  if (!booking.individual_bookings || booking.individual_bookings.length <= 1) {
    return false;
  }

  const timeRanges = groupConsecutiveTimeSlots(booking.individual_bookings);
  return timeRanges.length > 1;
}

/**
 * Gets detailed time breakdown for booking details modal
 * @param booking - GroupedBooking object
 * @returns Object with time details for detailed display
 */
export function getDetailedTimeBreakdown(booking: GroupedBooking) {
  const timeRanges = booking.individual_bookings 
    ? groupConsecutiveTimeSlots(booking.individual_bookings)
    : [`${formatTime(booking.start_time)} - ${formatTime(booking.end_time)}`];

  return {
    timeRanges,
    isNonContinuous: timeRanges.length > 1,
    totalDuration: booking.slot_count,
    summary: getBookingTimeSummary(booking)
  };
}

/**
 * Calculates the correct total price for a booking group by summing individual booking prices
 * @param booking - GroupedBooking object
 * @returns Correct total price
 */
export function calculateBookingGroupTotalPrice(booking: GroupedBooking): number {
  // If we have individual bookings data, sum their prices for accuracy
  if (booking.individual_bookings && booking.individual_bookings.length > 0) {
    return booking.individual_bookings.reduce((total, slot) => total + slot.total_price, 0);
  }
  
  // Fallback to the booking's total_price
  return booking.total_price;
}
